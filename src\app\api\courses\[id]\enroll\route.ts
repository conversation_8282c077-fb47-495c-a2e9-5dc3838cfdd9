import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb'
import Course, { CourseStatus } from '@/models/Course'
import Enrollment, { EnrollmentStatus, EnrollmentType } from '@/models/Enrollment'
import User from '@/models/User'
import { z } from 'zod'
import mongoose from 'mongoose'

// Validation schema for enrollment
const enrollmentSchema = z.object({
  type: z.enum(['free', 'paid', 'activation_code']),
  activationCode: z.string().optional(),
  paymentIntentId: z.string().optional() // For Stripe payments
})

// POST /api/courses/[id]/enroll - Đăng ký khóa học
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()
    
    const { id: courseId } = params
    
    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
      return NextResponse.json({
        success: false,
        error: 'ID khóa học không hợp lệ'
      }, { status: 400 })
    }
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập để đăng ký khóa học'
      }, { status: 401 })
    }
    
    // Parse request body
    const body = await request.json()
    const { type, activationCode, paymentIntentId } = enrollmentSchema.parse(body)
    
    // Find course
    const course = await Course.findById(courseId)
    if (!course) {
      return NextResponse.json({
        success: false,
        error: 'Không tìm thấy khóa học'
      }, { status: 404 })
    }
    
    // Check if course is published
    if (course.status !== CourseStatus.PUBLISHED) {
      return NextResponse.json({
        success: false,
        error: 'Khóa học chưa được xuất bản'
      }, { status: 400 })
    }
    
    // Check if user is already enrolled
    const existingEnrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId
    })
    
    if (existingEnrollment) {
      if (existingEnrollment.status === EnrollmentStatus.ACTIVE) {
        return NextResponse.json({
          success: false,
          error: 'Bạn đã đăng ký khóa học này rồi'
        }, { status: 400 })
      }
      
      // If enrollment exists but not active, reactivate it
      if (existingEnrollment.status === EnrollmentStatus.SUSPENDED) {
        existingEnrollment.status = EnrollmentStatus.ACTIVE
        existingEnrollment.lastAccessedAt = new Date()
        await existingEnrollment.save()
        
        return NextResponse.json({
          success: true,
          data: existingEnrollment,
          message: 'Đã kích hoạt lại khóa học thành công'
        })
      }
    }
    
    // Validate enrollment type
    let enrollmentData: any = {
      userId: session.user.id,
      courseId,
      status: EnrollmentStatus.ACTIVE,
      type: EnrollmentType.FREE,
      payment: {
        amount: 0,
        currency: 'VND',
        method: 'free'
      }
    }
    
    switch (type) {
      case 'free':
        // Check if course is actually free
        if (course.pricing.basePrice > 0) {
          return NextResponse.json({
            success: false,
            error: 'Khóa học này không miễn phí'
          }, { status: 400 })
        }
        break
        
      case 'paid':
        if (!paymentIntentId) {
          return NextResponse.json({
            success: false,
            error: 'Thiếu thông tin thanh toán'
          }, { status: 400 })
        }
        
        // TODO: Verify payment with Stripe
        // For now, we'll assume payment is successful
        enrollmentData.type = EnrollmentType.PAID
        enrollmentData.payment = {
          amount: course.pricing.basePrice,
          currency: course.pricing.currency,
          method: 'stripe',
          transactionId: paymentIntentId,
          paidAt: new Date()
        }
        break
        
      case 'activation_code':
        if (!activationCode) {
          return NextResponse.json({
            success: false,
            error: 'Thiếu mã kích hoạt'
          }, { status: 400 })
        }
        
        // TODO: Validate activation code
        // For now, we'll assume code is valid
        enrollmentData.type = EnrollmentType.ACTIVATION_CODE
        enrollmentData.payment = {
          amount: 0,
          currency: 'VND',
          method: 'activation_code',
          activationCode
        }
        break
        
      default:
        return NextResponse.json({
          success: false,
          error: 'Loại đăng ký không hợp lệ'
        }, { status: 400 })
    }
    
    // Create enrollment
    const enrollment = new Enrollment(enrollmentData)
    
    // Initialize progress
    await enrollment.updateProgress()
    await enrollment.save()
    
    // Update course stats
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'stats.totalStudents': 1 },
      'stats.lastUpdated': new Date()
    })
    
    // Populate enrollment data for response
    await enrollment.populate('courseId', 'title slug thumbnail')
    await enrollment.populate('userId', 'profile.firstName profile.lastName')
    
    return NextResponse.json({
      success: true,
      data: enrollment,
      message: 'Đăng ký khóa học thành công'
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error enrolling in course:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi đăng ký khóa học'
    }, { status: 500 })
  }
}

// GET /api/courses/[id]/enroll - Kiểm tra trạng thái đăng ký
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()
    
    const { id: courseId } = params
    
    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
      return NextResponse.json({
        success: false,
        error: 'ID khóa học không hợp lệ'
      }, { status: 400 })
    }
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        data: {
          isEnrolled: false,
          canEnroll: true,
          enrollment: null
        }
      })
    }
    
    // Find enrollment
    const enrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId
    })
    .populate('courseId', 'title slug pricing')
    .lean()
    
    const isEnrolled = enrollment && enrollment.status === EnrollmentStatus.ACTIVE
    const canEnroll = !isEnrolled
    
    return NextResponse.json({
      success: true,
      data: {
        isEnrolled,
        canEnroll,
        enrollment: isEnrolled ? enrollment : null
      }
    })
    
  } catch (error) {
    console.error('Error checking enrollment status:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi kiểm tra trạng thái đăng ký'
    }, { status: 500 })
  }
}

// DELETE /api/courses/[id]/enroll - Hủy đăng ký khóa học
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await connectDB()
    
    const { id: courseId } = params
    
    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
      return NextResponse.json({
        success: false,
        error: 'ID khóa học không hợp lệ'
      }, { status: 400 })
    }
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Find enrollment
    const enrollment = await Enrollment.findOne({
      userId: session.user.id,
      courseId
    })
    
    if (!enrollment) {
      return NextResponse.json({
        success: false,
        error: 'Bạn chưa đăng ký khóa học này'
      }, { status: 404 })
    }
    
    if (enrollment.status !== EnrollmentStatus.ACTIVE) {
      return NextResponse.json({
        success: false,
        error: 'Khóa học không ở trạng thái hoạt động'
      }, { status: 400 })
    }
    
    // Check if refund is possible (within 7 days and less than 20% progress)
    const enrollmentAge = Date.now() - enrollment.createdAt.getTime()
    const daysSinceEnrollment = enrollmentAge / (1000 * 60 * 60 * 24)
    const canRefund = daysSinceEnrollment <= 7 && enrollment.progress.completionPercentage < 20
    
    if (enrollment.type === EnrollmentType.PAID && !canRefund) {
      return NextResponse.json({
        success: false,
        error: 'Không thể hủy khóa học đã thanh toán sau 7 ngày hoặc khi đã học quá 20%'
      }, { status: 400 })
    }
    
    // Update enrollment status
    enrollment.status = EnrollmentStatus.REFUNDED
    await enrollment.save()
    
    // Update course stats
    await Course.findByIdAndUpdate(courseId, {
      $inc: { 'stats.totalStudents': -1 },
      'stats.lastUpdated': new Date()
    })
    
    // TODO: Process refund if applicable
    
    return NextResponse.json({
      success: true,
      message: 'Đã hủy đăng ký khóa học thành công'
    })
    
  } catch (error) {
    console.error('Error unenrolling from course:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi hủy đăng ký khóa học'
    }, { status: 500 })
  }
}
