import { NextRequest, NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb'
import User, { UserRole, UserStatus } from '@/models/User'
import { z } from 'zod'

// Validation schema
const registerSchema = z.object({
  email: z.string().email('<PERSON><PERSON> không hợp lệ'),
  password: z.string().min(8, 'Mật khẩu phải có ít nhất 8 ký tự'),
  firstName: z.string().min(1, 'Tên là bắt buộc').max(50, 'Tên không được vượt quá 50 ký tự'),
  lastName: z.string().min(1, 'Họ là bắt buộc').max(50, 'Họ không được vượt quá 50 ký tự'),
  role: z.enum(['student', 'instructor']).optional().default('student')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = registerSchema.parse(body)
    
    // Connect to database
    await connectDB()
    
    // Check if user already exists
    const existingUser = await User.findOne({ 
      email: validatedData.email.toLowerCase() 
    })
    
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email đã được sử dụng' },
        { status: 400 }
      )
    }
    
    // Create new user
    const user = new User({
      email: validatedData.email.toLowerCase(),
      password: validatedData.password,
      role: validatedData.role as UserRole,
      status: UserStatus.PENDING_VERIFICATION,
      profile: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        languagePreferences: {
          native: ['vietnamese'],
          learning: ['english']
        }
      },
      preferences: {
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        privacy: {
          profileVisibility: 'public',
          showProgress: true,
          showAchievements: true
        },
        learning: {
          dailyGoal: 30, // 30 minutes per day
          preferredDifficulty: 'medium'
        }
      }
    })
    
    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken()
    
    // Save user
    await user.save()
    
    // TODO: Send verification email
    console.log(`Verification token for ${user.email}: ${verificationToken}`)
    
    return NextResponse.json(
      { 
        message: 'Đăng ký thành công! Vui lòng kiểm tra email để xác thực tài khoản.',
        userId: user._id 
      },
      { status: 201 }
    )
    
  } catch (error: any) {
    console.error('Registration error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Dữ liệu không hợp lệ', details: error.errors },
        { status: 400 }
      )
    }
    
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Email đã được sử dụng' },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { error: 'Đã xảy ra lỗi trong quá trình đăng ký' },
      { status: 500 }
    )
  }
}
