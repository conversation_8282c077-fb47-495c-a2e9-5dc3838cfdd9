import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb'
import Enrollment, { EnrollmentStatus } from '@/models/Enrollment'
import User, { UserRole } from '@/models/User'

// GET /api/dashboard/student - Lấy dữ liệu dashboard cho học viên
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Verify user exists (allow all authenticated users to access student dashboard)
    const user = await User.findById(session.user.id)
    if (!user) {
      return NextResponse.json({
        success: false,
        error: '<PERSON>hông tìm thấy người dùng'
      }, { status: 404 })
    }
    
    // Get user enrollments with course details
    const enrollments = await Enrollment.find({
      userId: session.user.id,
      status: { $in: [EnrollmentStatus.ACTIVE, EnrollmentStatus.COMPLETED] }
    })
    .populate({
      path: 'courseId',
      select: 'title slug thumbnail instructor stats',
      populate: {
        path: 'instructor',
        select: 'profile.firstName profile.lastName profile.avatar'
      }
    })
    .sort({ lastAccessedAt: -1 })
    .lean()
    
    // Calculate dashboard statistics
    const stats = {
      totalCourses: enrollments.length,
      completedCourses: enrollments.filter(e => e.progress.status === 'completed').length,
      totalWatchTime: enrollments.reduce((total, e) => total + e.progress.totalWatchTime, 0),
      averageProgress: enrollments.length > 0 
        ? enrollments.reduce((total, e) => total + e.progress.completionPercentage, 0) / enrollments.length
        : 0
    }
    
    // Get recent activity (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    const recentActivity = enrollments.filter(e => 
      new Date(e.progress.lastAccessedAt) > sevenDaysAgo
    ).length
    
    // Get learning streak (consecutive days with activity)
    const learningStreak = await calculateLearningStreak(session.user.id)
    
    // Get upcoming deadlines (courses with access expiration)
    const upcomingDeadlines = enrollments
      .filter(e => e.accessExpiresAt && new Date(e.accessExpiresAt) > new Date())
      .sort((a, b) => new Date(a.accessExpiresAt!).getTime() - new Date(b.accessExpiresAt!).getTime())
      .slice(0, 5)
    
    // Get recommended courses (based on categories of enrolled courses)
    const enrolledCategories = enrollments
      .map(e => e.courseId?.category)
      .filter(Boolean)
    
    // Get achievements
    const achievements = calculateAchievements(enrollments, stats)
    
    return NextResponse.json({
      success: true,
      data: {
        enrollments,
        stats: {
          ...stats,
          recentActivity,
          learningStreak
        },
        upcomingDeadlines,
        achievements
      }
    })
    
  } catch (error) {
    console.error('Error fetching student dashboard:', error)
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi tải dashboard'
    }, { status: 500 })
  }
}

// Helper function to calculate learning streak
async function calculateLearningStreak(userId: string): Promise<number> {
  try {
    // Get enrollments with recent activity
    const enrollments = await Enrollment.find({
      userId,
      status: EnrollmentStatus.ACTIVE
    }).select('progress.lastAccessedAt').lean()
    
    if (enrollments.length === 0) return 0
    
    // Get unique activity dates (sorted descending)
    const activityDates = enrollments
      .map(e => {
        const date = new Date(e.progress.lastAccessedAt)
        return date.toDateString()
      })
      .filter((date, index, array) => array.indexOf(date) === index)
      .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
    
    if (activityDates.length === 0) return 0
    
    // Calculate consecutive days
    let streak = 0
    const today = new Date().toDateString()
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toDateString()
    
    // Check if there's activity today or yesterday
    if (activityDates[0] === today || activityDates[0] === yesterday) {
      streak = 1
      
      // Count consecutive days
      for (let i = 1; i < activityDates.length; i++) {
        const currentDate = new Date(activityDates[i-1])
        const nextDate = new Date(activityDates[i])
        const diffDays = Math.floor((currentDate.getTime() - nextDate.getTime()) / (1000 * 60 * 60 * 24))
        
        if (diffDays === 1) {
          streak++
        } else {
          break
        }
      }
    }
    
    return streak
  } catch (error) {
    console.error('Error calculating learning streak:', error)
    return 0
  }
}

// Helper function to calculate achievements
function calculateAchievements(enrollments: any[], stats: any) {
  const achievements = []
  
  // First course achievement
  if (stats.totalCourses >= 1) {
    achievements.push({
      id: 'first_course',
      title: 'Khóa học đầu tiên',
      description: 'Đã đăng ký khóa học đầu tiên',
      icon: '🎯',
      unlockedAt: enrollments[0]?.createdAt
    })
  }
  
  // Course completion achievements
  if (stats.completedCourses >= 1) {
    achievements.push({
      id: 'first_completion',
      title: 'Hoàn thành đầu tiên',
      description: 'Đã hoàn thành khóa học đầu tiên',
      icon: '🏆',
      unlockedAt: enrollments.find(e => e.progress.status === 'completed')?.updatedAt
    })
  }
  
  if (stats.completedCourses >= 5) {
    achievements.push({
      id: 'five_completions',
      title: 'Học viên tích cực',
      description: 'Đã hoàn thành 5 khóa học',
      icon: '🌟',
      unlockedAt: new Date()
    })
  }
  
  // Watch time achievements
  const totalHours = Math.floor(stats.totalWatchTime / 3600)
  if (totalHours >= 10) {
    achievements.push({
      id: 'ten_hours',
      title: 'Người học kiên trì',
      description: 'Đã học hơn 10 giờ',
      icon: '⏰',
      unlockedAt: new Date()
    })
  }
  
  if (totalHours >= 50) {
    achievements.push({
      id: 'fifty_hours',
      title: 'Chuyên gia học tập',
      description: 'Đã học hơn 50 giờ',
      icon: '🎓',
      unlockedAt: new Date()
    })
  }
  
  // Progress achievements
  if (stats.averageProgress >= 80) {
    achievements.push({
      id: 'high_progress',
      title: 'Người hoàn thành xuất sắc',
      description: 'Tiến độ trung bình trên 80%',
      icon: '📈',
      unlockedAt: new Date()
    })
  }
  
  return achievements.sort((a, b) => 
    new Date(b.unlockedAt).getTime() - new Date(a.unlockedAt).getTime()
  )
}
