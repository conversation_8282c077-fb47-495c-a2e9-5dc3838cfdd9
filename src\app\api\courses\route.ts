import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb'
import Course, { CourseStatus, CourseLevel, CourseLanguage } from '@/models/Course'
import Category from '@/models/Category'
import User, { UserRole } from '@/models/User'
import { z } from 'zod'

// Cache configuration
export const revalidate = 300 // 5 minutes

// Validation schemas
const createCourseSchema = z.object({
  title: z.string().min(1, 'Tiêu đề là bắt buộc').max(200, 'Tiêu đề không được vượt quá 200 ký tự'),
  shortDescription: z.string().min(1, '<PERSON><PERSON> tả ngắn là bắt buộc').max(500, 'Mô tả ngắn không được vượt quá 500 ký tự'),
  content: z.object({
    description: z.string().min(1, '<PERSON><PERSON> tả chi tiết là bắt buộc'),
    objectives: z.array(z.string()).min(1, '<PERSON><PERSON><PERSON> có ít nhất 1 mục tiêu'),
    prerequisites: z.array(z.string()),
    syllabus: z.array(z.object({
      week: z.number().min(1),
      title: z.string().min(1),
      topics: z.array(z.string()),
      duration: z.number().min(0)
    }))
  }),
  category: z.string().min(1, 'Danh mục là bắt buộc'),
  language: z.enum(Object.values(CourseLanguage) as [string, ...string[]]),
  level: z.enum(Object.values(CourseLevel) as [string, ...string[]]),
  pricing: z.object({
    basePrice: z.number().min(0, 'Giá không được âm'),
    currency: z.string().default('VND')
  }),
  thumbnail: z.string().url().optional(),
  tags: z.array(z.string()).optional()
})

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  category: z.string().optional(),
  level: z.string().optional(),
  language: z.string().optional(),
  status: z.string().optional(),
  instructor: z.string().optional(),
  sortBy: z.string().optional().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
  featured: z.string().optional().transform(val => val === 'true'),
  minPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined),
  maxPrice: z.string().optional().transform(val => val ? parseFloat(val) : undefined)
})

// GET /api/courses - Lấy danh sách courses với filtering và pagination
export async function GET(request: NextRequest) {
  try {
    await connectDB()
    
    const { searchParams } = new URL(request.url)
    const query = querySchema.parse(Object.fromEntries(searchParams))
    
    // Build filter object
    const filter: any = {}
    
    // Text search
    if (query.search) {
      filter.$or = [
        { title: { $regex: query.search, $options: 'i' } },
        { shortDescription: { $regex: query.search, $options: 'i' } },
        { 'content.description': { $regex: query.search, $options: 'i' } },
        { tags: { $in: [new RegExp(query.search, 'i')] } }
      ]
    }
    
    // Category filter
    if (query.category) {
      filter.category = query.category
    }
    
    // Level filter
    if (query.level) {
      filter.level = query.level
    }
    
    // Language filter
    if (query.language) {
      filter.language = query.language
    }
    
    // Status filter
    if (query.status) {
      filter.status = query.status
    } else {
      // Default to published courses for public access
      filter.status = CourseStatus.PUBLISHED
    }
    
    // Instructor filter
    if (query.instructor) {
      filter.instructor = query.instructor
    }
    
    // Featured filter
    if (query.featured) {
      filter['metadata.isFeatured'] = true
    }
    
    // Price range filter
    if (query.minPrice !== undefined || query.maxPrice !== undefined) {
      filter['pricing.basePrice'] = {}
      if (query.minPrice !== undefined) {
        filter['pricing.basePrice'].$gte = query.minPrice
      }
      if (query.maxPrice !== undefined) {
        filter['pricing.basePrice'].$lte = query.maxPrice
      }
    }
    
    // Build sort object
    const sort: any = {}
    sort[query.sortBy] = query.sortOrder === 'asc' ? 1 : -1
    
    // Calculate pagination
    const skip = (query.page - 1) * query.limit
    
    // Execute query with aggregation for better performance
    const [courses, totalCount] = await Promise.all([
      Course.find(filter)
        .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
        .populate('category', 'name slug')
        .sort(sort)
        .skip(skip)
        .limit(query.limit)
        .lean(),
      Course.countDocuments(filter)
    ])
    
    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / query.limit)
    const hasNextPage = query.page < totalPages
    const hasPrevPage = query.page > 1
    
    return NextResponse.json({
      success: true,
      data: {
        courses,
        pagination: {
          currentPage: query.page,
          totalPages,
          totalCount,
          limit: query.limit,
          hasNextPage,
          hasPrevPage
        }
      }
    })
    
  } catch (error) {
    console.error('Error fetching courses:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi lấy danh sách khóa học'
    }, { status: 500 })
  }
}

// POST /api/courses - Tạo course mới (chỉ instructor và admin)
export async function POST(request: NextRequest) {
  try {
    await connectDB()
    
    // Check authentication
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        error: 'Vui lòng đăng nhập'
      }, { status: 401 })
    }
    
    // Check user role
    const user = await User.findById(session.user.id)
    if (!user || (user.role !== UserRole.INSTRUCTOR && user.role !== UserRole.ADMIN)) {
      return NextResponse.json({
        success: false,
        error: 'Bạn không có quyền tạo khóa học'
      }, { status: 403 })
    }
    
    // Parse and validate request body
    const body = await request.json()
    const validatedData = createCourseSchema.parse(body)
    
    // Verify category exists
    const category = await Category.findById(validatedData.category)
    if (!category) {
      return NextResponse.json({
        success: false,
        error: 'Danh mục không tồn tại'
      }, { status: 400 })
    }
    
    // Create course
    const course = new Course({
      ...validatedData,
      instructor: session.user.id,
      slug: validatedData.title
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^a-z0-9\s-]/g, '')
        .trim()
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-'),
      status: CourseStatus.DRAFT,
      stats: {
        totalStudents: 0,
        totalLessons: 0,
        totalDuration: 0,
        averageRating: 0,
        totalRatings: 0,
        completionRate: 0,
        lastUpdated: new Date()
      }
    })
    
    await course.save()
    
    // Populate instructor and category info
    await course.populate('instructor', 'profile.firstName profile.lastName profile.avatar')
    await course.populate('category', 'name slug')
    
    return NextResponse.json({
      success: true,
      data: course,
      message: 'Khóa học đã được tạo thành công'
    }, { status: 201 })
    
  } catch (error) {
    console.error('Error creating course:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Dữ liệu không hợp lệ',
        details: error.errors
      }, { status: 400 })
    }
    
    // Handle duplicate slug error
    if (error.code === 11000 && error.keyPattern?.slug) {
      return NextResponse.json({
        success: false,
        error: 'Tiêu đề khóa học đã tồn tại, vui lòng chọn tiêu đề khác'
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: 'Lỗi server khi tạo khóa học'
    }, { status: 500 })
  }
}
